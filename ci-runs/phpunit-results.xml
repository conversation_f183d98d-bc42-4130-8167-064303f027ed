<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="Tests\Unit\FieldAvailabilityServiceTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityServiceTest.php" tests="12" assertions="51" errors="0" failures="2" skipped="0" time="0.111957">
    <testcase name="it_checks_if_field_is_available" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityServiceTest.php" line="35" class="Tests\Unit\FieldAvailabilityServiceTest" classname="Tests.Unit.FieldAvailabilityServiceTest" assertions="1" time="0.060292"/>
    <testcase name="it_detects_unavailable_field_due_to_existing_reservation" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityServiceTest.php" line="45" class="Tests\Unit\FieldAvailabilityServiceTest" classname="Tests.Unit.FieldAvailabilityServiceTest" assertions="2" time="0.005171"/>
    <testcase name="it_checks_working_hours_correctly" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityServiceTest.php" line="66" class="Tests\Unit\FieldAvailabilityServiceTest" classname="Tests.Unit.FieldAvailabilityServiceTest" assertions="4" time="0.002769"/>
    <testcase name="it_detects_conflicting_reservations" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityServiceTest.php" line="82" class="Tests\Unit\FieldAvailabilityServiceTest" classname="Tests.Unit.FieldAvailabilityServiceTest" assertions="5" time="0.003267"/>
    <testcase name="it_gets_available_time_slots" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityServiceTest.php" line="106" class="Tests\Unit\FieldAvailabilityServiceTest" classname="Tests.Unit.FieldAvailabilityServiceTest" assertions="6" time="0.003527"/>
    <testcase name="it_excludes_unavailable_slots_from_available_time_slots" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityServiceTest.php" line="125" class="Tests\Unit\FieldAvailabilityServiceTest" classname="Tests.Unit.FieldAvailabilityServiceTest" assertions="1" time="0.003729"/>
    <testcase name="it_gets_field_availability_calendar" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityServiceTest.php" line="149" class="Tests\Unit\FieldAvailabilityServiceTest" classname="Tests.Unit.FieldAvailabilityServiceTest" assertions="9" time="0.007426"/>
    <testcase name="it_gets_all_fields_availability" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityServiceTest.php" line="170" class="Tests\Unit\FieldAvailabilityServiceTest" classname="Tests.Unit.FieldAvailabilityServiceTest" assertions="2" time="0.006218">
      <failure type="PHPUnit\Framework\ExpectationFailedException">Tests\Unit\FieldAvailabilityServiceTest::it_gets_all_fields_availability
Failed asserting that actual size 2 matches expected size 3.

/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityServiceTest.php:183</failure>
      <system-out>3&lt;br&gt;2&lt;br&gt;</system-out>
    </testcase>
    <testcase name="it_validates_duration" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityServiceTest.php" line="193" class="Tests\Unit\FieldAvailabilityServiceTest" classname="Tests.Unit.FieldAvailabilityServiceTest" assertions="3" time="0.002802"/>
    <testcase name="it_finds_next_available_slot" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityServiceTest.php" line="201" class="Tests\Unit\FieldAvailabilityServiceTest" classname="Tests.Unit.FieldAvailabilityServiceTest" assertions="4" time="0.003327"/>
    <testcase name="it_validates_reservation_comprehensively" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityServiceTest.php" line="214" class="Tests\Unit\FieldAvailabilityServiceTest" classname="Tests.Unit.FieldAvailabilityServiceTest" assertions="1" time="0.003731">
      <failure type="PHPUnit\Framework\ExpectationFailedException">Tests\Unit\FieldAvailabilityServiceTest::it_validates_reservation_comprehensively
Failed asserting that false is true.

/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityServiceTest.php:220</failure>
    </testcase>
    <testcase name="it_gets_field_statistics" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Unit/FieldAvailabilityServiceTest.php" line="237" class="Tests\Unit\FieldAvailabilityServiceTest" classname="Tests.Unit.FieldAvailabilityServiceTest" assertions="13" time="0.009698"/>
  </testsuite>
</testsuites>
